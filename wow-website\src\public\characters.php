<?php
/**
 * Página de Personagens
 * 
 * <AUTHOR> Agent
 * @version 1.0
 */

require_once '../includes/config/config.php';

// Verificar se está logado
if (!isLoggedIn()) {
    redirect('/login?redirect=' . urlencode('/characters'));
}

// Obter personagens da conta
function getCharacters($account_id) {
    try {
        $chars_db = DatabaseManager::getConnection('characters');
        $stmt = $chars_db->prepare("
            SELECT guid, name, race, class, gender, level, zone, 
                   totaltime, leveltime, logout_time, online
            FROM characters 
            WHERE account = ?
            ORDER BY level DESC, name ASC
        ");
        $stmt->execute([$account_id]);
        return $stmt->fetchAll();
    } catch (Exception $e) {
        writeLog('ERROR', 'Error fetching characters: ' . $e->getMessage());
        return [];
    }
}

// Obter nomes das raças
function getRaceName($race_id) {
    $races = [
        1 => 'Humano', 2 => 'Orc', 3 => 'Anão', 4 => 'Elfo Noturno',
        5 => 'Morto-vivo', 6 => 'Tauren', 7 => 'Gnomo', 8 => 'Troll',
        10 => 'Elfo Sangrento', 11 => 'Draenei'
    ];
    return $races[$race_id] ?? 'Desconhecido';
}

// Obter nomes das classes
function getClassName($class_id) {
    $classes = [
        1 => 'Guerreiro', 2 => 'Paladino', 3 => 'Caçador', 4 => 'Ladino',
        5 => 'Sacerdote', 6 => 'Cavaleiro da Morte', 7 => 'Xamã', 8 => 'Mago',
        9 => 'Bruxo', 11 => 'Druida'
    ];
    return $classes[$class_id] ?? 'Desconhecida';
}

// Obter dados dos personagens
$characters = getCharacters($_SESSION['user_id']);
$total_characters = count($characters);
$max_level_char = !empty($characters) ? max(array_column($characters, 'level')) : 0;

?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Meus Personagens - AzerothNexus</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="/assets/css/style.css">
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <img src="/assets/images/logo.png" alt="Logo" width="32" height="32">
                AzerothNexus
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">Início</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/download">Download</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/ranking">Ranking</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/forum">Fórum</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/donate">Doações</a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i> <?= escape($_SESSION['username']) ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/account">Minha Conta</a></li>
                            <li><a class="dropdown-item active" href="/characters">Personagens</a></li>
                            <?php if (isAdmin()): ?>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="/admin">Painel Admin</a></li>
                            <?php endif; ?>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/logout">Sair</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <div class="page-header">
                        <h1><i class="fas fa-users"></i> Meus Personagens</h1>
                        <p class="text-muted">Gerencie seus personagens no servidor</p>
                    </div>
                </div>
            </div>

            <!-- Estatísticas -->
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-content">
                            <h3><?= $total_characters ?></h3>
                            <p>Total de Personagens</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-crown"></i>
                        </div>
                        <div class="stat-content">
                            <h3><?= $max_level_char ?></h3>
                            <p>Maior Nível</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-content">
                            <h3><?= count(array_filter($characters, function($c) { return $c['online']; })) ?></h3>
                            <p>Online Agora</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Lista de Personagens -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-list"></i> Lista de Personagens</h5>
                        </div>
                        <div class="card-body">
                            <?php if (empty($characters)): ?>
                                <div class="text-center py-5">
                                    <i class="fas fa-user-slash fa-3x text-muted mb-3"></i>
                                    <h4>Nenhum personagem encontrado</h4>
                                    <p class="text-muted">Você ainda não criou nenhum personagem no servidor.</p>
                                    <a href="/download" class="btn btn-primary">
                                        <i class="fas fa-download"></i> Baixar Cliente
                                    </a>
                                </div>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>Nome</th>
                                                <th>Raça</th>
                                                <th>Classe</th>
                                                <th>Nível</th>
                                                <th>Status</th>
                                                <th>Último Login</th>
                                                <th>Tempo Jogado</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($characters as $char): ?>
                                                <tr>
                                                    <td>
                                                        <strong><?= escape($char['name']) ?></strong>
                                                        <?php if ($char['online']): ?>
                                                            <span class="badge bg-success ms-2">Online</span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td><?= getRaceName($char['race']) ?></td>
                                                    <td><?= getClassName($char['class']) ?></td>
                                                    <td>
                                                        <span class="badge bg-primary"><?= $char['level'] ?></span>
                                                    </td>
                                                    <td>
                                                        <?php if ($char['online']): ?>
                                                            <span class="text-success">
                                                                <i class="fas fa-circle"></i> Online
                                                            </span>
                                                        <?php else: ?>
                                                            <span class="text-muted">
                                                                <i class="fas fa-circle"></i> Offline
                                                            </span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <?php if ($char['logout_time']): ?>
                                                            <?= date('d/m/Y H:i', $char['logout_time']) ?>
                                                        <?php else: ?>
                                                            <span class="text-muted">Nunca</span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <?php 
                                                        $hours = floor($char['totaltime'] / 3600);
                                                        $minutes = floor(($char['totaltime'] % 3600) / 60);
                                                        echo "{$hours}h {$minutes}m";
                                                        ?>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p>&copy; 2024 AzerothNexus. Todos os direitos reservados.</p>
                </div>
                <div class="col-md-6 text-end">
                    <p>World of Warcraft 3.3.5a</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="/assets/js/main.js"></script>
</body>
</html>
