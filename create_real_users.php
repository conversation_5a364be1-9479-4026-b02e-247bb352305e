<?php
// Script para criar usuários reais com senhas SRP6 corretas

function calculateSRP6Verifier($username, $password, $salt) {
    // Constantes SRP6 para WoW (padrão RFC 5054)
    $g = gmp_init(7);
    $N = gmp_init('894B645E89E1535BBDAD5B8B290650530801B18EBFBF5E8FAB3C82872A3E9BB7', 16);

    $username = strtoupper($username);
    $password = strtoupper($password);

    // Calcular hash da senha (H(username:password))
    $h1 = sha1($username . ':' . $password, true);

    // Combinar salt + h1 e calcular hash
    $h2 = sha1($salt . $h1, true);

    // Reverter bytes para little-endian (formato WoW)
    $h2_reversed = strrev($h2);

    // Converter para número grande
    $x = gmp_init('0x' . bin2hex($h2_reversed));

    // Calcular verifier: g^x mod N
    $v = gmp_powm($g, $x, $N);

    // Converter para binário (32 bytes, little-endian)
    $verifier_hex = gmp_strval($v, 16);
    if (strlen($verifier_hex) % 2 != 0) {
        $verifier_hex = '0' . $verifier_hex;
    }

    $verifier = hex2bin($verifier_hex);
    $verifier = strrev($verifier);

    return str_pad($verifier, 32, "\0", STR_PAD_RIGHT);
}

try {
    $pdo = new PDO('mysql:host=localhost;dbname=acore_auth', 'acore', 'acore');
    
    // Limpar usuários de teste antigos
    $pdo->exec("DELETE FROM account WHERE username IN ('testuser', 'admin')");
    
    // Criar usuário de teste: testuser / 123456
    $username = 'testuser';
    $password = '123456';
    $salt = random_bytes(32);
    $verifier = calculateSRP6Verifier($username, $password, $salt);
    
    $stmt = $pdo->prepare("
        INSERT INTO account (username, salt, verifier, email, reg_mail, expansion) 
        VALUES (?, ?, ?, ?, ?, ?)
    ");
    $stmt->execute([
        $username,
        $salt,
        $verifier,
        '<EMAIL>',
        '<EMAIL>',
        2
    ]);
    
    echo "✅ Usuário criado: testuser / 123456<br>";
    
    // Criar usuário admin: admin / admin123
    $username = 'admin';
    $password = 'admin123';
    $salt = random_bytes(32);
    $verifier = calculateSRP6Verifier($username, $password, $salt);
    
    $stmt->execute([
        $username,
        $salt,
        $verifier,
        '<EMAIL>',
        '<EMAIL>',
        2
    ]);
    
    echo "✅ Usuário criado: admin / admin123<br>";
    
    // Verificar usuários criados
    $stmt = $pdo->query("SELECT id, username, email FROM account ORDER BY id");
    echo "<br>✅ Usuários no banco:<br>";
    while ($row = $stmt->fetch()) {
        echo "- ID: {$row['id']}, Username: {$row['username']}, Email: {$row['email']}<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Erro: " . $e->getMessage();
}
?>
