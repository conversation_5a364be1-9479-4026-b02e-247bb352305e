<?php
/**
 * Teste de Navegação Pós-Login
 * 
 * <AUTHOR> Agent
 * @version 1.0
 */

session_start();

echo "<!DOCTYPE html>";
echo "<html><head><title>🧪 Teste de Navegação</title>";
echo "<style>
body { font-family: Arial; margin: 20px; background: #1a1a2e; color: #eee; }
.test-section { background: #16213e; padding: 20px; margin: 10px 0; border-radius: 8px; }
.success { color: #4CAF50; }
.error { color: #f44336; }
.warning { color: #ff9800; }
.info { color: #2196F3; }
.nav-test { background: #0f3460; padding: 15px; margin: 10px 0; border-radius: 5px; }
.nav-test a { color: #ffd700; text-decoration: none; margin: 0 10px; padding: 8px 15px; background: #e94560; border-radius: 5px; }
.nav-test a:hover { background: #c73650; }
</style></head><body>";

echo "<h1>🧪 TESTE DE NAVEGAÇÃO PÓS-LOGIN</h1>";

// Simular login
$_SESSION['user_id'] = 1;
$_SESSION['username'] = 'cavalcrod';
$_SESSION['email'] = '<EMAIL>';
$_SESSION['user_level'] = 0;
$_SESSION['login_time'] = time();

echo "<div class='test-section'>";
echo "<h2>1. 📊 STATUS DA SESSÃO</h2>";
echo "<div class='info'>✅ user_id: " . ($_SESSION['user_id'] ?? 'NÃO DEFINIDO') . "</div>";
echo "<div class='info'>✅ username: " . ($_SESSION['username'] ?? 'NÃO DEFINIDO') . "</div>";
echo "<div class='info'>✅ login_time: " . ($_SESSION['login_time'] ?? 'NÃO DEFINIDO') . "</div>";
echo "<div class='info'>⏰ Timestamp atual: " . time() . "</div>";
echo "</div>";

// Incluir config
require_once 'src/includes/config/config.php';

echo "<div class='test-section'>";
echo "<h2>2. 🔐 VERIFICAÇÃO DE LOGIN</h2>";

if (function_exists('isLoggedIn')) {
    $logged_in = isLoggedIn();
    if ($logged_in) {
        echo "<div class='success'>✅ isLoggedIn() = TRUE - Usuário está logado</div>";
    } else {
        echo "<div class='error'>❌ isLoggedIn() = FALSE - Usuário NÃO está logado</div>";
    }
} else {
    echo "<div class='error'>❌ Função isLoggedIn() não encontrada</div>";
}
echo "</div>";

echo "<div class='test-section'>";
echo "<h2>3. 🧭 TESTE DE NAVEGAÇÃO</h2>";
echo "<p>Clique nos links abaixo para testar a navegação:</p>";

echo "<div class='nav-test'>";
echo "<a href='/account' target='_blank'>👤 Minha Conta</a>";
echo "<a href='/characters' target='_blank'>⚔️ Personagens</a>";
echo "<a href='/ranking' target='_blank'>🏆 Ranking</a>";
echo "<a href='/download' target='_blank'>⬇️ Download</a>";
echo "<a href='/forum' target='_blank'>💬 Fórum</a>";
echo "</div>";
echo "</div>";

echo "<div class='test-section'>";
echo "<h2>4. 📁 VERIFICAÇÃO DE ARQUIVOS</h2>";

$files_to_check = [
    'src/public/account.php' => 'Minha Conta',
    'src/public/characters.php' => 'Personagens',
    'src/public/ranking.php' => 'Ranking',
    'src/public/download.php' => 'Download',
    'src/public/forum.php' => 'Fórum',
    'src/public/logout.php' => 'Logout'
];

foreach ($files_to_check as $file => $name) {
    if (file_exists($file)) {
        echo "<div class='success'>✅ $name ($file) - Arquivo existe</div>";
    } else {
        echo "<div class='error'>❌ $name ($file) - Arquivo NÃO existe</div>";
    }
}
echo "</div>";

echo "<div class='test-section'>";
echo "<h2>5. 🔧 CONFIGURAÇÕES</h2>";
echo "<div class='info'>SESSION_TIMEOUT: " . (defined('SESSION_TIMEOUT') ? SESSION_TIMEOUT . ' segundos' : 'NÃO DEFINIDO') . "</div>";
echo "<div class='info'>MAX_LOGIN_ATTEMPTS: " . (defined('MAX_LOGIN_ATTEMPTS') ? MAX_LOGIN_ATTEMPTS : 'NÃO DEFINIDO') . "</div>";
echo "<div class='info'>LOGIN_LOCKOUT_TIME: " . (defined('LOGIN_LOCKOUT_TIME') ? LOGIN_LOCKOUT_TIME . ' segundos' : 'NÃO DEFINIDO') . "</div>";
echo "</div>";

echo "<div class='test-section'>";
echo "<h2>6. 🚀 TESTE DIRETO DE REDIRECIONAMENTO</h2>";
echo "<p>Teste direto das funções de redirecionamento:</p>";

// Teste da função redirect
if (function_exists('redirect')) {
    echo "<div class='success'>✅ Função redirect() está disponível</div>";
    echo "<button onclick=\"testRedirect('/account')\" style='background: #4CAF50; color: white; padding: 10px; border: none; border-radius: 5px; cursor: pointer;'>🧪 Testar Redirect para /account</button>";
    echo "<script>
    function testRedirect(url) {
        if (confirm('Testar redirecionamento para ' + url + '?')) {
            window.location.href = url;
        }
    }
    </script>";
} else {
    echo "<div class='error'>❌ Função redirect() NÃO está disponível</div>";
}
echo "</div>";

echo "<div class='test-section'>";
echo "<h2>7. 🔄 AÇÕES DE TESTE</h2>";
echo "<div style='margin: 10px 0;'>";
echo "<a href='debug_session.php' style='background: #2196F3; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin: 5px;'>🔍 Debug Sessão</a>";
echo "<a href='src/public/logout.php' style='background: #f44336; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin: 5px;'>🚪 Fazer Logout</a>";
echo "<a href='src/public/login.php' style='background: #4CAF50; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin: 5px;'>🔑 Ir para Login</a>";
echo "</div>";
echo "</div>";

echo "<div class='test-section'>";
echo "<h2>8. 📝 LOGS RECENTES</h2>";
echo "<p>Verificando logs do sistema...</p>";

$log_file = 'logs/app.log';
if (file_exists($log_file)) {
    $logs = file_get_contents($log_file);
    $recent_logs = array_slice(explode("\n", $logs), -10);
    echo "<div style='background: #000; padding: 10px; border-radius: 5px; font-family: monospace; font-size: 12px;'>";
    foreach ($recent_logs as $log) {
        if (trim($log)) {
            echo htmlspecialchars($log) . "<br>";
        }
    }
    echo "</div>";
} else {
    echo "<div class='warning'>⚠️ Arquivo de log não encontrado: $log_file</div>";
}
echo "</div>";

echo "</body></html>";
?>
