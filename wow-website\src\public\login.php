<?php
/**
 * Página de Login
 * 
 * <AUTHOR> Agent
 * @version 1.0
 */

require_once '../includes/config/config.php';

// Se já estiver logado, redirecionar
if (isLoggedIn()) {
    redirect('/account');
}

$errors = [];
$login_attempts_key = 'login_attempts_' . getRealIP();

// DEBUG: Verificar se é POST
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    writeLog('DEBUG', 'POST recebido', [
        'username' => $_POST['username'] ?? 'VAZIO',
        'password_length' => strlen($_POST['password'] ?? ''),
        'csrf_token' => $_POST['csrf_token'] ?? 'VAZIO'
    ]);
} else {
    writeLog('DEBUG', 'Método não é POST', ['method' => $_SERVER['REQUEST_METHOD']]);
}

// Verificar tentativas de login
if (isset($_SESSION[$login_attempts_key]) && $_SESSION[$login_attempts_key] >= MAX_LOGIN_ATTEMPTS) {
    $lockout_time = $_SESSION['lockout_time_' . getRealIP()] ?? 0;
    if (time() - $lockout_time < LOGIN_LOCKOUT_TIME) {
        $remaining = LOGIN_LOCKOUT_TIME - (time() - $lockout_time);
        $errors[] = "Muitas tentativas de login. Tente novamente em " . ceil($remaining / 60) . " minutos.";
    } else {
        // Reset tentativas após lockout
        unset($_SESSION[$login_attempts_key]);
        unset($_SESSION['lockout_time_' . getRealIP()]);
    }
}

// Processar formulário
if ($_SERVER['REQUEST_METHOD'] === 'POST' && empty($errors)) {
    // Verificar CSRF
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $errors[] = 'Token de segurança inválido.';
    } else {
        // Sanitizar dados
        $username = sanitizeInput($_POST['username'] ?? '', 'username');
        $password = $_POST['password'] ?? '';
        $remember = isset($_POST['remember']);
        
        // Validações básicas
        if (empty($username)) {
            $errors[] = 'Nome de usuário é obrigatório.';
        }
        
        if (empty($password)) {
            $errors[] = 'Senha é obrigatória.';
        }
        
        // Tentar fazer login
        if (empty($errors)) {
            try {
                $auth_db = DatabaseManager::getConnection('auth');
                
                // Buscar conta
                // Adicionado sha_pass_hash para suportar migração de contas legadas
                $stmt = $auth_db->prepare("
                    SELECT id, username, salt, verifier, sha_pass_hash, email, expansion, locked
                    FROM account
                    WHERE username = ?
                ");
                $stmt->execute([$username]);
                $account = $stmt->fetch();
                
                if ($account) {
                    // Verificar se conta está bloqueada
                    if ($account['locked']) {
                        $errors[] = 'Conta bloqueada. Entre em contato com a administração.';
                    } else {
                        $login_successful = false;

                        // TENTATIVA 1: Autenticação moderna (SRP6)
                        if (!empty($account['salt']) && !empty($account['verifier'])) {
                            $calculated_verifier = calculateSRP6Verifier($username, $password, $account['salt']);
                            if (hash_equals($account['verifier'], $calculated_verifier)) {
                                $login_successful = true;
                            }
                        }

                        // TENTATIVA 2: Fallback para autenticação legada (SHA1) e migração
                        if (!$login_successful && !empty($account['sha_pass_hash'])) {
                            $legacy_hash = strtoupper(sha1(strtoupper($username) . ':' . strtoupper($password)));
                            if (hash_equals($account['sha_pass_hash'], $legacy_hash)) {
                                $login_successful = true;

                                // MIGRAÇÃO DA CONTA PARA SRP6
                                try {
                                    writeLog('INFO', 'Migrating legacy account to SRP6', ['username' => $username]);
                                    
                                    $new_salt = random_bytes(32);
                                    $new_verifier = calculateSRP6Verifier($username, $password, $new_salt);

                                    $migrate_stmt = $auth_db->prepare("
                                        UPDATE account 
                                        SET salt = ?, verifier = ?, sha_pass_hash = NULL 
                                        WHERE id = ?
                                    ");
                                    $migrate_stmt->execute([$new_salt, $new_verifier, $account['id']]);
                                    
                                    writeLog('INFO', 'Account migration successful', ['username' => $username]);
                                } catch (Exception $migration_e) {
                                    writeLog('ERROR', 'Failed to migrate legacy account', [
                                        'username' => $username, 
                                        'error' => $migration_e->getMessage()
                                    ]);
                                }
                            }
                        }

                        // Se o login foi bem-sucedido por qualquer método
                        if ($login_successful) {
                            session_regenerate_id(true);
                            $_SESSION['user_id'] = $account['id'];
                            $_SESSION['username'] = $account['username'];
                            $_SESSION['email'] = $account['email'];
                            $_SESSION['user_level'] = $account['expansion'] ?? 0;
                            $_SESSION['login_time'] = time();

                            unset($_SESSION[$login_attempts_key]);
                            unset($_SESSION['lockout_time_' . getRealIP()]);

                            $update_stmt = $auth_db->prepare("
                                UPDATE account SET last_login = NOW(), last_ip = ? WHERE id = ?
                            ");
                            $update_stmt->execute([getRealIP(), $account['id']]);

                            if ($remember) {
                                $token = bin2hex(random_bytes(32));
                                setcookie('remember_token', $token, time() + (30 * 24 * 60 * 60), '/', '', true, true);
                            }

                            writeLog('INFO', 'User logged in', ['username' => $username, 'ip' => getRealIP()]);

                            $redirect_to = $_GET['redirect'] ?? '/account';
                            redirect($redirect_to);
                        } else {
                            $errors[] = 'Nome de usuário ou senha incorretos.';
                        }
                    }
                } else {
                    $errors[] = 'Nome de usuário ou senha incorretos.';
                }
                
            } catch (Exception $e) {
                $errors[] = 'Erro ao fazer login. Tente novamente.';
                writeLog('ERROR', 'Login error: ' . $e->getMessage());
            }
        }
        
        // Incrementar tentativas de login em caso de erro
        if (!empty($errors)) {
            $_SESSION[$login_attempts_key] = ($_SESSION[$login_attempts_key] ?? 0) + 1;
            if ($_SESSION[$login_attempts_key] >= MAX_LOGIN_ATTEMPTS) {
                $_SESSION['lockout_time_' . getRealIP()] = time();
            }
        }
    }
}

// Dados para o template
$page_data = [
    'title' => 'Login - ' . SITE_NAME,
    'description' => 'Faça login no ' . SITE_NAME,
    'current_page' => 'login'
];

?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= escape($page_data['title']) ?></title>
    <meta name="description" content="<?= escape($page_data['description']) ?>">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="<?= ASSETS_PATH ?>/css/style.css" rel="stylesheet">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?= ASSETS_PATH ?>/images/favicon.ico">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark fixed-top">
        <div class="container">
            <a class="navbar-brand" href="/">
                <img src="<?= ASSETS_PATH ?>/images/logo.png" alt="Logo" height="40">
                <span class="ms-2"><?= SITE_NAME ?></span>
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">Início</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/download">Download</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/ranking">Ranking</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/forum">Fórum</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/donate">Doações</a>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link active" href="/login">Login</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/register">Registrar</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

<style>
/* Estilos específicos para página de login */
.login-container {
    background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
    min-height: 100vh;
    padding-top: 100px;
    color: var(--text-light);
}

.login-card {
    background: rgba(26, 26, 46, 0.95);
    border: 2px solid var(--primary-color);
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.5);
    backdrop-filter: blur(10px);
}

.login-header {
    background: linear-gradient(135deg, var(--primary-color), #e6b800);
    color: var(--secondary-color);
    border-radius: 13px 13px 0 0;
    padding: 20px;
    text-align: center;
    font-weight: bold;
}

.form-control {
    background: rgba(255,255,255,0.1);
    border: 1px solid var(--primary-color);
    color: var(--text-light);
    border-radius: 8px;
}

.form-control:focus {
    background: rgba(255,255,255,0.15);
    border-color: var(--primary-color);
    box-shadow: 0 0 10px rgba(244, 196, 48, 0.3);
    color: var(--text-light);
}

.form-control::placeholder {
    color: rgba(255,255,255,0.6);
}

.btn-login {
    background: linear-gradient(135deg, var(--primary-color), #e6b800);
    border: none;
    color: var(--secondary-color);
    font-weight: bold;
    padding: 12px;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.btn-login:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(244, 196, 48, 0.4);
    color: var(--secondary-color);
}

.alert-success {
    background: rgba(40, 167, 69, 0.2);
    border: 1px solid var(--success-color);
    color: var(--success-color);
}

.alert-danger {
    background: rgba(220, 53, 69, 0.2);
    border: 1px solid var(--danger-color);
    color: var(--danger-color);
}

.form-label {
    color: var(--primary-color);
    font-weight: 500;
}

.register-link {
    color: var(--primary-color);
    text-decoration: none;
}

.register-link:hover {
    color: #e6b800;
    text-decoration: underline;
}

.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.form-check-label {
    color: rgba(255,255,255,0.8);
}
</style>

<div class="login-container">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-4">
                <div class="login-card">
                    <div class="login-header">
                        <h4><i class="fas fa-sign-in-alt me-2"></i>Entrar</h4>
                        <p class="mb-0">Acesse sua conta</p>
                    </div>
                    <div class="p-4">
                        <?php if (!empty($errors)): ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <ul class="mb-0">
                                    <?php foreach ($errors as $error): ?>
                                        <li><?= escape($error) ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        <?php endif; ?>

                        <form method="POST" id="loginForm" action="/login.php<?= isset($_GET['redirect']) ? '?redirect=' . urlencode($_GET['redirect']) : '' ?>">
                            <input type="hidden" name="csrf_token" value="<?= generateCSRFToken() ?>">

                            <div class="mb-3">
                                <label for="username" class="form-label">
                                    <i class="fas fa-user me-2"></i>Nome de Usuário
                                </label>
                                <input type="text" class="form-control" id="username" name="username"
                                       placeholder="Digite seu nome de usuário"
                                       value="<?= escape($_POST['username'] ?? '') ?>" required>
                            </div>

                            <div class="mb-3">
                                <label for="password" class="form-label">
                                    <i class="fas fa-lock me-2"></i>Senha
                                </label>
                                <input type="password" class="form-control" id="password" name="password"
                                       placeholder="Digite sua senha" required>
                            </div>

                            <div class="mb-4 form-check">
                                <input type="checkbox" class="form-check-input" id="remember" name="remember">
                                <label class="form-check-label" for="remember">
                                    Lembrar-me
                                </label>
                            </div>

                            <div class="d-grid mb-3">
                                <button type="submit" class="btn btn-login">
                                    <i class="fas fa-sign-in-alt me-2"></i>Entrar
                                </button>
                            </div>
                        </form>

                        <div class="text-center">
                            <p class="mb-0">Não tem uma conta?
                                <a href="/register" class="register-link">
                                    <i class="fas fa-user-plus me-1"></i>Registre-se aqui
                                </a>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

    <!-- Footer -->
    <footer class="bg-dark text-light py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5><?= SITE_NAME ?></h5>
                    <p class="mb-0">© <?= date('Y') ?> Todos os direitos reservados.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">World of Warcraft é uma marca registrada da Blizzard Entertainment.</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

<?php
/**
 * Calcular SRP6 Verifier para AzerothCore
 * Implementação compatível com AzerothCore/TrinityCore
 */
function calculateSRP6Verifier($username, $password, $salt) {
    // Constantes SRP6 para WoW (padrão RFC 5054)
    $g = gmp_init(7);
    $N = gmp_init('894B645E89E1535BBDAD5B8B290650530801B18EBFBF5E8FAB3C82872A3E9BB7', 16);

    $username = strtoupper($username);
    $password = strtoupper($password);

    // Calcular hash da senha (H(username:password))
    $h1 = sha1($username . ':' . $password, true);

    // Combinar salt + h1 e calcular hash
    $h2 = sha1($salt . $h1, true);

    // Reverter bytes para little-endian (formato WoW)
    $h2_reversed = strrev($h2);

    // Converter para número grande
    $x = gmp_init('0x' . bin2hex($h2_reversed));

    // Calcular verifier: v = g^x mod N
    $v = gmp_powm($g, $x, $N);

    // Converter de volta para 32 bytes em little-endian
    $verifier_hex = gmp_strval($v, 16);

    // Garantir que tem tamanho par
    if (strlen($verifier_hex) % 2 != 0) {
        $verifier_hex = '0' . $verifier_hex;
    }

    // Converter para binário
    $verifier = hex2bin($verifier_hex);

    // Reverter para little-endian
    $verifier = strrev($verifier);

    // Garantir 32 bytes
    return str_pad($verifier, 32, "\0", STR_PAD_RIGHT);
}
?>
