# Configuração Nginx para Site WoW 3.3.5a
# 
# <AUTHOR> Agent
# @version 1.0

server {
    listen 80;
    listen [::]:80;
    server_name SERVER_NAME;
    
    # Redirect HTTP to HTTPS (descomente em produção)
    # return 301 https://$server_name$request_uri;
}

server {
    # listen 443 ssl http2;
    # listen [::]:443 ssl http2;
    listen 80;  # Para desenvolvimento
    server_name SERVER_NAME;
    
    root DOCUMENT_ROOT/public;
    index index.php index.html index.htm;
    
    # SSL Configuration (descomente em produção)
    # ssl_certificate /etc/ssl/certs/wow-site.crt;
    # ssl_certificate_key /etc/ssl/private/wow-site.key;
    # ssl_protocols TLSv1.2 TLSv1.3;
    # ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    # ssl_prefer_server_ciphers off;
    # ssl_session_cache shared:SSL:10m;
    # ssl_session_timeout 10m;
    
    # Security Headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' cdn.jsdelivr.net cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' cdn.jsdelivr.net cdnjs.cloudflare.com fonts.googleapis.com; font-src 'self' fonts.gstatic.com cdnjs.cloudflare.com; img-src 'self' data: https:; connect-src 'self'; frame-src 'self';" always;
    
    # HSTS (descomente em produção com HTTPS)
    # add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    # Gzip Compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        application/atom+xml
        application/geo+json
        application/javascript
        application/x-javascript
        application/json
        application/ld+json
        application/manifest+json
        application/rdf+xml
        application/rss+xml
        application/xhtml+xml
        application/xml
        font/eot
        font/otf
        font/ttf
        image/svg+xml
        text/css
        text/javascript
        text/plain
        text/xml;
    
    # Cache Control
    location ~* \.(jpg|jpeg|png|gif|ico|css|js|woff|woff2|ttf|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }
    
    # PHP Processing
    location ~ \.php$ {
        include snippets/fastcgi-php.conf;
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
        
        # Security
        fastcgi_hide_header X-Powered-By;
        
        # Timeouts
        fastcgi_connect_timeout 60s;
        fastcgi_send_timeout 60s;
        fastcgi_read_timeout 60s;
        
        # Buffer sizes
        fastcgi_buffer_size 128k;
        fastcgi_buffers 4 256k;
        fastcgi_busy_buffers_size 256k;
    }
    
    # API Routes
    location /api/ {
        try_files $uri $uri/ /api/index.php?$query_string;
        
        # CORS Headers para API
        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "GET, POST, OPTIONS" always;
        add_header Access-Control-Allow-Headers "Content-Type, Authorization" always;
        
        # Handle preflight requests
        if ($request_method = 'OPTIONS') {
            add_header Access-Control-Allow-Origin "*";
            add_header Access-Control-Allow-Methods "GET, POST, OPTIONS";
            add_header Access-Control-Allow-Headers "Content-Type, Authorization";
            add_header Content-Length 0;
            add_header Content-Type text/plain;
            return 204;
        }
    }
    
    # Admin Panel (adicionar autenticação básica se necessário)
    location /admin/ {
        try_files $uri $uri/ /admin/index.php?$query_string;
        
        # Basic Auth (descomente e configure se necessário)
        # auth_basic "Admin Area";
        # auth_basic_user_file /etc/nginx/.htpasswd;
    }
    
    # Specific page redirects
    location = /register {
        return 301 /register.php;
    }

    location = /login {
        return 301 /login.php;
    }

    location = /account {
        return 301 /account.php;
    }

    location = /characters {
        return 301 /characters.php;
    }

    location = /ranking {
        return 301 /ranking.php;
    }

    location = /download {
        return 301 /download.php;
    }

    location = /forum {
        return 301 /forum.php;
    }

    location = /donate {
        return 301 /donate.php;
    }

    location = /logout {
        return 301 /logout.php;
    }

    # Pretty URLs
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }
    
    # Deny access to sensitive files
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    location ~ /(config|includes|templates|logs|\.env) {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    location ~ \.(sql|log|conf)$ {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # Deny access to backup files
    location ~ \.(bak|backup|old|orig|save|swp|tmp)$ {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # Rate Limiting
    limit_req_zone $binary_remote_addr zone=login:10m rate=5r/m;
    limit_req_zone $binary_remote_addr zone=api:10m rate=60r/m;
    limit_req_zone $binary_remote_addr zone=general:10m rate=10r/s;
    
    location /login {
        limit_req zone=login burst=3 nodelay;
        try_files $uri $uri/ /index.php?$query_string;
    }
    
    location /api/ {
        limit_req zone=api burst=10 nodelay;
    }
    
    # General rate limiting
    limit_req zone=general burst=20 nodelay;
    
    # Logs
    access_log /var/log/nginx/wow-site-access.log;
    error_log /var/log/nginx/wow-site-error.log;
    
    # Error Pages
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;
    
    location = /404.html {
        root DOCUMENT_ROOT/public/errors;
        internal;
    }
    
    location = /50x.html {
        root DOCUMENT_ROOT/public/errors;
        internal;
    }
    
    # Favicon
    location = /favicon.ico {
        log_not_found off;
        access_log off;
    }
    
    # Robots.txt
    location = /robots.txt {
        allow all;
        log_not_found off;
        access_log off;
    }
    
    # Health Check
    location = /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
    
    # Block common exploits
    location ~* (wp-admin|wp-login|xmlrpc|phpmyadmin) {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # Block user agents
    if ($http_user_agent ~* (bot|crawler|spider|scraper)) {
        return 403;
    }
    
    # Block bad requests
    if ($request_method !~ ^(GET|HEAD|POST)$) {
        return 405;
    }
}
