<?php
/**
 * Teste de Registro SRP6
 * Verifica se o registro está criando contas com salt e verifier
 */

require_once 'includes/config/config.php';

echo "<h1>🧪 Teste de Registro SRP6</h1>\n";

// Dados de teste
$test_username = 'teste_srp6_' . time();
$test_password = 'senha123';
$test_email = 'teste' . time() . '@example.com';

echo "<h2>📋 Dados do Teste:</h2>\n";
echo "<p><strong>Username:</strong> $test_username</p>\n";
echo "<p><strong>Password:</strong> $test_password</p>\n";
echo "<p><strong>Email:</strong> $test_email</p>\n";

try {
    $auth_db = DatabaseManager::getConnection('auth');
    echo "<p>✅ Conexão com banco estabelecida</p>\n";
    
    // Simular o processo de registro
    echo "<h2>🔧 Simulando Registro:</h2>\n";
    
    // Gerar salt e verifier (formato AzerothCore moderno)
    $salt = random_bytes(32);
    $verifier = calculateSRP6Verifier($test_username, $test_password, $salt);
    
    echo "<p>✅ Salt gerado: " . bin2hex($salt) . "</p>\n";
    echo "<p>✅ Verifier calculado: " . bin2hex($verifier) . "</p>\n";
    
    // Inserir conta
    $stmt = $auth_db->prepare("
        INSERT INTO account (username, salt, verifier, email, joindate, last_ip)
        VALUES (?, ?, ?, ?, NOW(), ?)
    ");
    
    $client_ip = '127.0.0.1';
    $stmt->execute([$test_username, $salt, $verifier, $test_email, $client_ip]);
    
    $account_id = $auth_db->lastInsertId();
    echo "<p>✅ Conta criada com ID: $account_id</p>\n";
    
    // Verificar se a conta foi criada corretamente
    echo "<h2>🔍 Verificando Conta Criada:</h2>\n";
    
    $stmt = $auth_db->prepare("
        SELECT id, username, salt, verifier, sha_pass_hash, email 
        FROM account 
        WHERE id = ?
    ");
    $stmt->execute([$account_id]);
    $account = $stmt->fetch();
    
    if ($account) {
        echo "<p>✅ Conta encontrada no banco</p>\n";
        echo "<p><strong>ID:</strong> " . $account['id'] . "</p>\n";
        echo "<p><strong>Username:</strong> " . $account['username'] . "</p>\n";
        echo "<p><strong>Email:</strong> " . $account['email'] . "</p>\n";
        echo "<p><strong>Salt:</strong> " . ($account['salt'] ? 'PRESENTE (' . strlen($account['salt']) . ' bytes)' : 'AUSENTE') . "</p>\n";
        echo "<p><strong>Verifier:</strong> " . ($account['verifier'] ? 'PRESENTE (' . strlen($account['verifier']) . ' bytes)' : 'AUSENTE') . "</p>\n";
        echo "<p><strong>SHA Pass Hash:</strong> " . ($account['sha_pass_hash'] ? 'PRESENTE (LEGADO!)' : 'AUSENTE (CORRETO!)') . "</p>\n";
        
        if ($account['salt'] && $account['verifier'] && !$account['sha_pass_hash']) {
            echo "<h3 style='color: green;'>🎉 SUCESSO: Conta criada com SRP6 moderno!</h3>\n";
        } else {
            echo "<h3 style='color: red;'>❌ ERRO: Conta não está no formato SRP6 correto!</h3>\n";
        }
    } else {
        echo "<p style='color: red;'>❌ Conta não encontrada no banco!</p>\n";
    }
    
    // Limpar conta de teste
    echo "<h2>🧹 Limpeza:</h2>\n";
    $stmt = $auth_db->prepare("DELETE FROM account WHERE id = ?");
    $stmt->execute([$account_id]);
    echo "<p>✅ Conta de teste removida</p>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Erro: " . $e->getMessage() . "</p>\n";
    echo "<p>Stack trace: " . $e->getTraceAsString() . "</p>\n";
}

/**
 * Função SRP6 para teste
 */
function calculateSRP6Verifier($username, $password, $salt) {
    // Constantes SRP6 para WoW (padrão RFC 5054)
    $g = gmp_init(7);
    $N = gmp_init('894B645E89E1535BBDAD5B8B290650530801B18EBFBF5E8FAB3C82872A3E9BB7', 16);

    $username = strtoupper($username);
    $password = strtoupper($password);

    // Calcular hash da senha (H(username:password))
    $h1 = sha1($username . ':' . $password, true);

    // Combinar salt + h1 e calcular hash
    $h2 = sha1($salt . $h1, true);

    // Reverter bytes para little-endian (formato WoW)
    $h2_reversed = strrev($h2);

    // Converter para número grande
    $x = gmp_init('0x' . bin2hex($h2_reversed));

    // Calcular verifier: g^x mod N
    $v = gmp_powm($g, $x, $N);

    // Converter verifier para formato binário
    $verifier_hex = gmp_strval($v, 16);
    
    // Garantir que o hex tenha número par de caracteres
    if (strlen($verifier_hex) % 2 != 0) {
        $verifier_hex = '0' . $verifier_hex;
    }

    // Converter para binário e reverter (little-endian)
    $verifier = hex2bin($verifier_hex);
    $verifier = strrev($verifier);

    // Pad para 32 bytes
    return str_pad($verifier, 32, "\0", STR_PAD_RIGHT);
}

echo "<p><em>Teste concluído em " . date('Y-m-d H:i:s') . "</em></p>\n";
?>
