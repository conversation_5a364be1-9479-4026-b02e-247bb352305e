# 🔐 Guia: SSH sem Senha - Configuração e Uso

## 📋 Índice
1. [<PERSON><PERSON><PERSON> Geral](#visão-geral)
2. [Configuração Realizada](#configuração-realizada)
3. [<PERSON>ar](#como-usar)
4. [Comandos Práticos](#comandos-práticos)
5. [Troubleshooting](#troubleshooting)
6. [<PERSON><PERSON><PERSON><PERSON>](#segurança)
7. [Backup e Recuperação](#backup-e-recuperação)

---

## 🎯 Visão Geral

Este documento explica como usar a configuração SSH sem senha criada para o servidor **Azeroth-Nexus**.

### **Informações do Sistema:**
- **Servidor**: Azeroth-Nexus (************)
- **Usuário**: root
- **Chave SSH**: azeroth-nexus-server
- **Tipo**: RSA 4096 bits
- **Status**: ✅ Configurado e funcionando
- **Última Verificação**: 2025-07-23 20:49 - TODOS OS TESTES PASSARAM

---

## 🎉 CONFIGURAÇÃO VALIDADA E FUNCIONANDO

### **✅ Testes Realizados em 2025-07-23 20:49:**
```bash
# TODOS OS TESTES SSH OBRIGATÓRIOS PASSARAM:
ssh azeroth-nexus "echo 'SSH OK'"                    # ✅ PASSOU
ssh azeroth-nexus "hostname && whoami && uptime"     # ✅ PASSOU
ssh azeroth-nexus "df -h"                           # ✅ PASSOU
ssh azeroth-nexus "systemctl status nginx"          # ✅ PASSOU
ssh azeroth-nexus "curl -s -o /dev/null -w '%{http_code}' http://localhost"  # ✅ PASSOU

# TODOS OS TESTES DEPLOY OBRIGATÓRIOS PASSARAM:
bash deploy/deploy.sh staging                       # ✅ PASSOU
bash deploy/deploy.sh production                    # ✅ PASSOU
ssh azeroth-nexus "curl -s http://localhost | head -5"  # ✅ PASSOU
```

### **🔑 Chave SSH Correta Identificada:**
- **Chave que FUNCIONA**: `azeroth-nexus-server`
- **Chaves disponíveis testadas**:
  - ❌ `id_rsa_rlponto` (pedia senha)
  - ✅ `azeroth-nexus-server` (funcionou sem senha)
  - `ainexus_servers_key` (não testada)

### **📋 Configuração SSH Final (VALIDADA):**
```
Host azeroth-nexus
    HostName ************
    User root
    IdentityFile ~/.ssh/azeroth-nexus-server
    IdentitiesOnly yes
    StrictHostKeyChecking no
    UserKnownHostsFile /dev/null
```

---

## ⚙️ Configuração Realizada

### **1. Chave SSH Criada:**
```bash
# No servidor (************)
ssh-keygen -t rsa -b 4096 -f ~/.ssh/azeroth-nexus-server -N "" -C "azeroth-nexus-server-key"
```

### **2. Arquivos Gerados:**
```
Servidor (************):
├── /root/.ssh/azeroth-nexus-server      # Chave privada
├── /root/.ssh/azeroth-nexus-server.pub  # Chave pública
└── /root/.ssh/authorized_keys           # Chave pública autorizada

Cliente Windows:
├── C:\Users\<USER>\.ssh\azeroth-nexus-server  # Chave privada (copiada)
└── C:\Users\<USER>\.ssh\config                # Configuração SSH
```

### **3. Configuração SSH (config):**
```
Host azeroth-nexus
    HostName ************
    User root
    IdentityFile ~/.ssh/azeroth-nexus-server
    IdentitiesOnly yes
    StrictHostKeyChecking no
```

### **4. Permissões Configuradas:**
```bash
# No servidor
chmod 700 ~/.ssh
chmod 600 ~/.ssh/authorized_keys
chmod 600 ~/.ssh/azeroth-nexus-server

# No Windows
icacls C:\Users\<USER>\.ssh\azeroth-nexus-server /inheritance:r /grant:r "user:R"
```

---

## 🚀 Como Usar

### **Método 1: Usando Alias (Recomendado)**
```bash
# Conectar usando o alias configurado
ssh azeroth-nexus

# Executar comando remoto
ssh azeroth-nexus "comando"

# Exemplo prático
ssh azeroth-nexus "hostname && uptime"
```

### **Método 2: Usando Chave Diretamente**
```bash
# Conectar especificando a chave
ssh -i ~/.ssh/azeroth-nexus-server root@************

# Executar comando remoto
ssh -i ~/.ssh/azeroth-nexus-server root@************ "comando"
```

### **Método 3: SCP (Cópia de Arquivos)**
```bash
# Copiar arquivo para o servidor
scp -i ~/.ssh/azeroth-nexus-server arquivo.txt root@************:/caminho/destino/

# Copiar arquivo do servidor
scp -i ~/.ssh/azeroth-nexus-server root@************:/caminho/arquivo.txt ./

# Usando alias
scp arquivo.txt azeroth-nexus:/caminho/destino/
```

### **Método 4: RSYNC**
```bash
# Sincronizar diretórios
rsync -avz -e "ssh -i ~/.ssh/azeroth-nexus-server" ./local/ root@************:/remoto/

# Usando alias
rsync -avz ./local/ azeroth-nexus:/remoto/
```

---

## 💻 Comandos Práticos

### **Verificar Conexão:**
```bash
# Teste básico
ssh azeroth-nexus "echo 'Conexão OK'"

# Verificar informações do sistema
ssh azeroth-nexus "hostname && whoami && uptime"

# Verificar espaço em disco
ssh azeroth-nexus "df -h"
```

### **Administração Remota:**
```bash
# Atualizar sistema
ssh azeroth-nexus "apt update && apt upgrade -y"

# Verificar serviços
ssh azeroth-nexus "systemctl status nginx"

# Ver logs
ssh azeroth-nexus "tail -f /var/log/syslog"
```

### **Deploy e Transferências:**
```bash
# Deploy de arquivos
rsync -avz --delete ./site/ azeroth-nexus:/var/www/html/

# Backup de configurações
scp azeroth-nexus:/etc/nginx/nginx.conf ./backup/

# Executar script remoto
ssh azeroth-nexus "bash -s" < script-local.sh
```

### **Túneis SSH:**
```bash
# Túnel local (porta 8080 local -> 80 remoto)
ssh -L 8080:localhost:80 azeroth-nexus

# Túnel reverso (porta 3000 remoto -> 3000 local)
ssh -R 3000:localhost:3000 azeroth-nexus
```

---

## 🛠️ Troubleshooting

### **Problema: "Permission denied (publickey)"**
```bash
# Verificar permissões da chave
ls -la ~/.ssh/azeroth-nexus-server

# Corrigir permissões (Linux/WSL)
chmod 600 ~/.ssh/azeroth-nexus-server

# Corrigir permissões (Windows)
icacls C:\Users\<USER>\.ssh\azeroth-nexus-server /inheritance:r /grant:r "user:R"
```

### **Problema: "Host key verification failed"**
```bash
# Remover host conhecido
ssh-keygen -R ************

# Ou conectar ignorando verificação
ssh -o StrictHostKeyChecking=no azeroth-nexus
```

### **Problema: Chave não encontrada**
```bash
# Verificar se a chave existe
ls -la ~/.ssh/azeroth-nexus-server

# Verificar configuração SSH
cat ~/.ssh/config

# Testar com debug
ssh -v azeroth-nexus
```

### **Problema: Conexão lenta**
```bash
# Desabilitar DNS reverso
ssh -o UseDNS=no azeroth-nexus

# Usar compressão
ssh -C azeroth-nexus
```

---

## 🔒 Segurança

### **Boas Práticas:**
1. **Nunca compartilhe a chave privada**
2. **Use senhas fortes para chaves importantes**
3. **Mantenha backups seguros das chaves**
4. **Monitore acessos SSH regularmente**
5. **Use fail2ban para proteção contra ataques**

### **Configurações de Segurança Adicionais:**
```bash
# No servidor, editar /etc/ssh/sshd_config
PermitRootLogin prohibit-password  # Apenas chaves SSH para root
PasswordAuthentication no          # Desabilitar senha
PubkeyAuthentication yes          # Habilitar chaves públicas
AuthorizedKeysFile .ssh/authorized_keys
```

### **Monitoramento:**
```bash
# Ver tentativas de login
ssh azeroth-nexus "grep 'ssh' /var/log/auth.log | tail -20"

# Ver conexões ativas
ssh azeroth-nexus "who"

# Ver histórico de comandos
ssh azeroth-nexus "history | tail -10"
```

---

## 💾 Backup e Recuperação

### **Backup das Chaves:**
```bash
# Criar backup das chaves
mkdir -p ~/backup/ssh-keys/$(date +%Y%m%d)
cp ~/.ssh/azeroth-nexus-server ~/backup/ssh-keys/$(date +%Y%m%d)/
cp ~/.ssh/config ~/backup/ssh-keys/$(date +%Y%m%d)/

# Backup compactado
tar -czf ~/backup/ssh-backup-$(date +%Y%m%d).tar.gz ~/.ssh/
```

### **Recuperação:**
```bash
# Restaurar chaves
cp ~/backup/ssh-keys/20250722/azeroth-nexus-server ~/.ssh/
chmod 600 ~/.ssh/azeroth-nexus-server

# Restaurar configuração
cp ~/backup/ssh-keys/20250722/config ~/.ssh/
```

### **Regenerar Chaves (se necessário):**
```bash
# 1. No servidor, gerar nova chave
ssh root@************  # (usando senha)
ssh-keygen -t rsa -b 4096 -f ~/.ssh/azeroth-nexus-new -N ""
cat ~/.ssh/azeroth-nexus-new.pub >> ~/.ssh/authorized_keys

# 2. Copiar nova chave privada para cliente
# 3. Atualizar ~/.ssh/config com novo arquivo
# 4. Testar nova chave
# 5. Remover chave antiga
```

---

## 📊 Exemplos de Uso Avançado

### **Script de Deploy Automatizado:**
```bash
#!/bin/bash
# deploy.sh

echo "🚀 Iniciando deploy..."

# Upload de arquivos
rsync -avz --delete \
    --exclude='.git' \
    --exclude='node_modules' \
    ./site/ azeroth-nexus:/var/www/html/

# Reiniciar serviços
ssh azeroth-nexus "systemctl reload nginx"

# Verificar status
ssh azeroth-nexus "curl -s -o /dev/null -w '%{http_code}' http://localhost"

echo "✅ Deploy concluído!"
```

### **Monitoramento Remoto:**
```bash
#!/bin/bash
# monitor.sh

echo "📊 Status do Servidor Azeroth-Nexus:"
echo "=================================="

# CPU e Memória
ssh azeroth-nexus "top -bn1 | head -5"

# Espaço em disco
ssh azeroth-nexus "df -h | grep -E '^/dev/'"

# Serviços críticos
ssh azeroth-nexus "systemctl is-active nginx mysql php8.1-fpm"

# Conexões ativas
ssh azeroth-nexus "ss -tuln | grep :80"
```

### **Backup Automatizado:**
```bash
#!/bin/bash
# backup.sh

BACKUP_DIR="/backup/$(date +%Y%m%d)"

# Criar backup no servidor
ssh azeroth-nexus "mkdir -p $BACKUP_DIR"
ssh azeroth-nexus "tar -czf $BACKUP_DIR/website.tar.gz /var/www/html/"
ssh azeroth-nexus "mysqldump --all-databases > $BACKUP_DIR/databases.sql"

# Baixar backup
scp azeroth-nexus:$BACKUP_DIR/* ./backups/

echo "✅ Backup concluído!"
```

---

## 🔧 Configuração do Deploy Script

### **Atualizar Script de Deploy:**
```bash
# Editar deploy/deploy.sh para usar a nova chave
SSH_KEY="$HOME/.ssh/azeroth-nexus-server"
SERVER_HOST="************"
SERVER_USER="root"
```

### **Teste Final:**
```bash
# Testar conexão
ssh azeroth-nexus "echo 'SSH funcionando perfeitamente!'"

# Testar deploy
./deploy/deploy.sh production
```

---

**Documento criado em**: 2025-07-22
**Última atualização**: 2025-07-23 20:49
**Autor**: Augment Agent
**Versão**: 1.1
**Servidor**: Azeroth-Nexus (************)
**Chave SSH**: azeroth-nexus-server (RSA 4096)
**Status**: ✅ VALIDADO E FUNCIONANDO
