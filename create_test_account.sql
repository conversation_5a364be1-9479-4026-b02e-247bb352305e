-- C<PERSON>r banco de dados e tabela de contas para teste
USE acore_auth;

-- <PERSON><PERSON>r tabela account se não existir
CREATE TABLE IF NOT EXISTS account (
    id INT(10) UNSIGNED NOT NULL AUTO_INCREMENT,
    username VARCHAR(32) NOT NULL DEFAULT '',
    salt BINARY(32) NOT NULL,
    verifier BINARY(32) NOT NULL,
    session_key BINARY(40) DEFAULT NULL,
    totp_secret VARBINARY(128) DEFAULT NULL,
    email VARCHAR(254) NOT NULL DEFAULT '',
    reg_mail VARCHAR(254) NOT NULL DEFAULT '',
    joindate TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_ip VARCHAR(15) NOT NULL DEFAULT '127.0.0.1',
    last_attempt_ip VARCHAR(15) NOT NULL DEFAULT '127.0.0.1',
    failed_logins INT(10) UNSIGNED NOT NULL DEFAULT '0',
    locked TINYINT(3) UNSIGNED NOT NULL DEFAULT '0',
    lock_country VARCHAR(2) NOT NULL DEFAULT '00',
    last_login TIMESTAMP NULL DEFAULT NULL,
    online TINYINT(3) UNSIGNED NOT NULL DEFAULT '0',
    expansion TINYINT(3) UNSIGNED NOT NULL DEFAULT '2',
    mutetime BIGINT(20) NOT NULL DEFAULT '0',
    mutereason VARCHAR(255) NOT NULL DEFAULT '',
    muteby VARCHAR(50) NOT NULL DEFAULT '',
    locale TINYINT(3) UNSIGNED NOT NULL DEFAULT '0',
    os VARCHAR(3) NOT NULL DEFAULT '',
    recruiter INT(10) UNSIGNED NOT NULL DEFAULT '0',
    PRIMARY KEY (id),
    UNIQUE KEY idx_username (username)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Account System';

-- Criar usuário de teste
-- Username: testuser
-- Password: testpass
-- Salt e verifier calculados para SRP6

INSERT IGNORE INTO account (
    username, 
    salt, 
    verifier, 
    email, 
    reg_mail, 
    expansion
) VALUES (
    'testuser',
    UNHEX('1234567890ABCDEF1234567890ABCDEF1234567890ABCDEF1234567890ABCDEF'),
    UNHEX('ABCDEF1234567890ABCDEF1234567890ABCDEF1234567890ABCDEF1234567890'),
    '<EMAIL>',
    '<EMAIL>',
    2
);

-- Criar usuário admin de teste
INSERT IGNORE INTO account (
    username, 
    salt, 
    verifier, 
    email, 
    reg_mail, 
    expansion
) VALUES (
    'admin',
    UNHEX('FEDCBA0987654321FEDCBA0987654321FEDCBA0987654321FEDCBA0987654321'),
    UNHEX('0987654321FEDCBA0987654321FEDCBA0987654321FEDCBA0987654321FEDCBA'),
    '<EMAIL>',
    '<EMAIL>',
    2
);

-- Verificar se os usuários foram criados
SELECT id, username, email, joindate FROM account WHERE username IN ('testuser', 'admin');
