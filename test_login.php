<?php
// Teste de login direto
session_start();

// Simular dados de login
$_POST['username'] = 'testuser';
$_POST['password'] = '123456';
$_POST['csrf_token'] = 'test';
$_SERVER['REQUEST_METHOD'] = 'POST';

// Incluir o sistema de login
require_once '../includes/config/config.php';

echo "<h2>🔍 Teste de Login</h2>";
echo "<p><strong>Usuário:</strong> testuser</p>";
echo "<p><strong>Senha:</strong> 123456</p>";

try {
    $auth_db = DatabaseManager::getConnection('auth');
    echo "✅ Conexão com banco OK<br>";
    
    // Buscar conta
    $stmt = $auth_db->prepare("
        SELECT id, username, salt, verifier, email, expansion, locked
        FROM account
        WHERE username = ?
    ");
    $stmt->execute(['testuser']);
    $account = $stmt->fetch();
    
    if ($account) {
        echo "✅ Usuário encontrado: " . $account['username'] . "<br>";
        echo "✅ Email: " . $account['email'] . "<br>";
        echo "✅ Salt: " . bin2hex($account['salt']) . "<br>";
        echo "✅ Verifier: " . bin2hex($account['verifier']) . "<br>";
        
        // Testar verificação de senha
        $calculated_verifier = calculateSRP6Verifier('testuser', '123456', $account['salt']);
        echo "✅ Verifier calculado: " . bin2hex($calculated_verifier) . "<br>";
        
        if (hash_equals($account['verifier'], $calculated_verifier)) {
            echo "🎉 <strong style='color: green;'>LOGIN FUNCIONANDO!</strong><br>";
        } else {
            echo "❌ <strong style='color: red;'>Senha incorreta</strong><br>";
        }
        
    } else {
        echo "❌ Usuário não encontrado<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Erro: " . $e->getMessage() . "<br>";
}
?>
