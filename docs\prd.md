# Documento de Requisitos do Produto (PRD) - Refatoração do Sistema de Autenticação

**Versão:** 1.0
**Autor:** Gemini Code Assist
**Data:** 24/05/2024

---

## 1. Visão Geral e Objetivo

O objetivo deste projeto é refatorar, unificar e modernizar o sistema de autenticação de usuários do website AzerothNexus. Atualmente, o sistema apresenta uma arquitetura fragmentada, com dois mecanismos de autenticação coexistindo (um legado baseado em `sha_pass_hash` e um moderno baseado em `SRP6`), além de uma estrutura de arquivos duplicada.

A conclusão deste projeto resultará em um sistema de login e registro mais **seguro**, **confiável**, **performático** e de **fácil manutenção**, alinhado com as melhores práticas do AzerothCore.

---

## 2. Análise do Estado Atual

A análise do código-fonte revelou os seguintes pontos críticos:

*   **2.1. Duplicidade de Mecanismos de Autenticação:**
    *   O arquivo `public/login.php` utiliza um método de hash legado: `strtoupper(sha1(strtoupper($username) . ':' . strtoupper($password)))`.
    *   O arquivo `src/public/login.php` tenta utilizar o método moderno SRP6, buscando as colunas `salt` e `verifier` no banco de dados.
    *   O registro em `public/register.php` cria contas com `sha_pass_hash`, enquanto `src/public/register.php` cria contas com `salt` e `verifier`.
    *   A existência do script `public/test_login_debug.php` é uma forte evidência de que essa inconsistência já está causando problemas de login.

*   **2.2. Estrutura de Arquivos Duplicada:**
    *   Existem versões quase idênticas dos principais arquivos (`login.php`, `register.php`, `forum.php`) nos diretórios `public/` e `src/public/`.
    *   Isso indica um problema no processo de build/deploy. O diretório `src/` deveria ser a única fonte da verdade, e o `public/` deveria ser o destino do deploy. A edição manual de arquivos em `public/` perpetua a inconsistência.

*   **2.3. Inconsistências no Acesso ao Banco de Dados:**
    *   O arquivo `public/login.php` instancia uma conexão PDO diretamente (`new PDO(...)`), ignorando a classe `DatabaseManager` que provavelmente faz parte da arquitetura padrão do projeto (usada nos arquivos do diretório `src/`).

*   **2.4. UI/UX Desatualizada na Versão Legada:**
    *   Os arquivos em `public/` possuem um layout e estilo mais simples, enquanto os arquivos em `src/public/` apresentam uma interface mais moderna e estilizada, indicando que esta é a versão desejada.

---

## 3. Problema a Ser Resolvido

A situação atual gera:

*   **Falhas de Login:** Usuários registrados por um método não conseguem logar pelo outro.
*   **Vulnerabilidades de Segurança:** O método `sha1` é considerado inseguro para armazenamento de senhas. A conexão direta com o banco pode expor credenciais no código.
*   **Dificuldade de Manutenção:** Desenvolvedores precisam lidar com duas lógicas diferentes, aumentando a complexidade e a chance de erros.
*   **Experiência do Usuário Inconsistente:** A aparência do site pode mudar dependendo de qual arquivo está sendo servido.

---

## 4. Requisitos da Solução Proposta

Para resolver os problemas identificados, a solução deve atender aos seguintes requisitos:

*   **REQ-001: Padronização do Método de Autenticação:** O sistema deve usar **exclusivamente o método SRP6** (`salt` e `verifier`) para novas contas e para verificação de login. Este é o padrão moderno e seguro do AzerothCore.

*   **REQ-002: Unificação da Base de Código:** O diretório `src/` deve ser definido como a **única fonte da verdade**. O diretório `public/` deve ser tratado como um artefato de build e não deve ser editado manualmente. O processo de deploy deve garantir que `public/` seja um espelho de `src/public/`.

*   **REQ-003: Consistência no Acesso a Dados:** Todas as consultas ao banco de dados devem ser realizadas através do `DatabaseManager`. A conexão direta via `new PDO(...)` deve ser removida.

*   **REQ-004: Manutenção da UI/UX Moderna:** A interface definida nos arquivos de `src/public/` deve ser a padrão para todo o site.

*   **REQ-005: Migração Transparente de Contas Legadas:** Para não invalidar as contas existentes, o sistema de login deve ser capaz de:
    1.  Tentar autenticar o usuário com SRP6.
    2.  Se falhar, e a conta possuir um `sha_pass_hash`, tentar autenticar com o método legado.
    3.  Se a autenticação legada for bem-sucedida, o sistema deve **imediatamente** gerar o `salt` e `verifier` a partir da senha fornecida, atualizar a conta no banco de dados e remover o `sha_pass_hash` antigo.

---

## 5. Plano de Implementação Sugerido

A implementação deve seguir as fases abaixo, respeitando as regras do `LEIA-INICIO.md`.

1.  **Fase 1: Preparação e Limpeza (Estrutural)**
    *   **Backup:** Fazer backup completo do banco de dados `acore_auth` e dos arquivos do projeto.
    *   **Limpeza:** Remover os arquivos conflitantes de `public/` (`login.php`, `register.php`, etc.).
    *   **Configuração do Deploy:** Revisar e corrigir o script `deploy/deploy.sh` para garantir que ele copie os arquivos de `src/public/` para `public/` corretamente.

2.  **Fase 2: Atualização do Código (Lógica)**
    *   **Registro (`src/public/register.php`):** Garantir que o formulário de registro crie contas **apenas** com `salt` e `verifier` (a versão em `src/` já parece fazer isso).
    *   **Login (`src/public/login.php`):** Implementar a lógica de migração transparente descrita no **REQ-005**.

3.  **Fase 3: Testes (Validação)**
    *   Executar todos os testes obrigatórios do `LEIA-INICIO.md`.
    *   Testar o fluxo de registro de um novo usuário.
    *   Testar o login de um usuário recém-criado (autenticação SRP6).
    *   Testar o login de um usuário antigo (com `sha_pass_hash`) e verificar se a conta é migrada para SRP6 no banco de dados.
    *   Testar o login do mesmo usuário antigo uma segunda vez para garantir que a autenticação SRP6 agora funcione diretamente.

4.  **Fase 4: Deploy (Produção)**
    *   Executar o deploy para `staging` e depois para `production` usando os scripts.

5.  **Fase 5: Monitoramento**
    *   Após o deploy, monitorar os logs de erro do PHP e do Nginx para identificar qualquer problema inesperado.

---

## 6. Critérios de Aceitação

O projeto será considerado concluído quando:

*   Novos usuários podem se registrar e logar com sucesso.
*   Usuários com contas legadas (`sha_pass_hash`) podem logar, e suas contas são automaticamente e transparentemente atualizadas para o formato SRP6.
*   O diretório `public/` é um reflexo exato do conteúdo de `src/public/` após o deploy.
*   Não há mais código com `new PDO(...)` nos arquivos de front-end.
*   Todas as páginas de autenticação utilizam a UI/UX moderna.

---

## 7. Riscos e Mitigações

*   **Risco:** Usuários existentes ficarem impossibilitados de logar.
    *   **Mitigação:** A estratégia de migração transparente (**REQ-005**) e testes exaustivos na Fase 3.
*   **Risco:** Perda de dados durante a migração de contas.
    *   **Mitigação:** Backup completo do banco de dados antes de iniciar o processo.

---

## 8. Próximos Passos

1.  Revisão e aprovação deste PRD pela equipe.
2.  Execução da Fase 1 do plano de implementação.