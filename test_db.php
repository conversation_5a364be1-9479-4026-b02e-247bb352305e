<?php
// Teste simples de conexão com banco de dados
try {
    $pdo = new PDO('mysql:host=localhost;dbname=acore_auth', 'acore', 'acore');
    echo "✅ Conexão com banco OK<br>";
    
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM account");
    $result = $stmt->fetch();
    echo "✅ Total de contas: " . $result['total'] . "<br>";
    
    $stmt = $pdo->query("SELECT username, email FROM account LIMIT 3");
    echo "✅ Usuários encontrados:<br>";
    while ($row = $stmt->fetch()) {
        echo "- " . $row['username'] . " (" . $row['email'] . ")<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Erro: " . $e->getMessage();
}
?>
